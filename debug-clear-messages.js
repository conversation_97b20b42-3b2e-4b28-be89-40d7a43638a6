// Debug script để test clear messages functionality
// Paste vào browser console để test

console.log('🔧 Starting debug script for clear messages...');

// 1. Test dispatch clearMessages event
function testClearMessages() {
    console.log('🧪 Testing clearMessages event...');
    window.dispatchEvent(new CustomEvent('clearMessages', {
        detail: { reason: 'debug_test', conversationId: '0' }
    }));
    console.log('✅ clearMessages event dispatched');
}

// 2. Test dispatch conversationChange event
function testConversationChange() {
    console.log('🧪 Testing conversationChange event...');
    window.dispatchEvent(new CustomEvent('conversationChange', {
        detail: { conversationId: '0' }
    }));
    console.log('✅ conversationChange event dispatched');
}

// 3. Check if event listeners are registered
function checkEventListeners() {
    console.log('🔍 Checking event listeners...');
    
    // Get all event listeners (this might not work in all browsers)
    const events = getEventListeners ? getEventListeners(window) : 'getEventListeners not available';
    console.log('📋 Window event listeners:', events);
    
    // Test if our events are working
    let clearMessagesCalled = false;
    let conversationChangeCalled = false;
    
    const testClearHandler = () => {
        clearMessagesCalled = true;
        console.log('✅ clearMessages listener is working');
    };
    
    const testConversationHandler = () => {
        conversationChangeCalled = true;
        console.log('✅ conversationChange listener is working');
    };
    
    window.addEventListener('clearMessages', testClearHandler);
    window.addEventListener('conversationChange', testConversationHandler);
    
    // Dispatch test events
    window.dispatchEvent(new CustomEvent('clearMessages', { detail: { test: true } }));
    window.dispatchEvent(new CustomEvent('conversationChange', { detail: { test: true } }));
    
    // Clean up test listeners
    window.removeEventListener('clearMessages', testClearHandler);
    window.removeEventListener('conversationChange', testConversationHandler);
    
    console.log('📊 Test results:', {
        clearMessages: clearMessagesCalled,
        conversationChange: conversationChangeCalled
    });
}

// 4. Simulate "Bắt đầu chủ đề mới" click
function simulateNewConversation() {
    console.log('🎭 Simulating "Bắt đầu chủ đề mới" click...');
    
    // Step 1: Dispatch clearMessages
    console.log('Step 1: Dispatching clearMessages...');
    window.dispatchEvent(new CustomEvent('clearMessages', {
        detail: { reason: 'new_conversation', conversationId: '0' }
    }));
    
    // Step 2: Update URL
    console.log('Step 2: Updating URL...');
    window.history.replaceState(null, '', '/chat/0');
    
    // Step 3: Dispatch conversationChange
    console.log('Step 3: Dispatching conversationChange...');
    window.dispatchEvent(new CustomEvent('conversationChange', {
        detail: { conversationId: '0' }
    }));
    
    console.log('✅ Simulation complete');
}

// 5. Check current state
function checkCurrentState() {
    console.log('📊 Current state:');
    console.log('URL:', window.location.pathname);
    console.log('Messages in DOM:', document.querySelectorAll('[data-message]').length);
    
    // Try to find React components (this is a hack and might not work)
    const chatInterface = document.querySelector('[data-testid="chat-interface"]') || 
                         document.querySelector('.chat-interface') ||
                         document.querySelector('main');
    
    if (chatInterface) {
        console.log('Chat interface found:', chatInterface);
        
        // Look for message elements
        const messageElements = chatInterface.querySelectorAll('.message, [class*="message"], [class*="chat"]');
        console.log('Message elements found:', messageElements.length);
    }
}

// 6. Monitor events
function monitorEvents() {
    console.log('👁️ Starting event monitoring...');
    
    const originalDispatch = window.dispatchEvent;
    window.dispatchEvent = function(event) {
        if (event.type === 'clearMessages' || event.type === 'conversationChange') {
            console.log(`🔔 Event dispatched: ${event.type}`, event.detail);
        }
        return originalDispatch.call(this, event);
    };
    
    // Listen for our custom events
    window.addEventListener('clearMessages', (e) => {
        console.log('🧹 clearMessages received:', e.detail);
    });
    
    window.addEventListener('conversationChange', (e) => {
        console.log('🔄 conversationChange received:', e.detail);
    });
    
    console.log('✅ Event monitoring started');
}

// Auto-run functions
console.log('🚀 Running debug checks...');
checkCurrentState();
checkEventListeners();
monitorEvents();

// Expose functions globally for manual testing
window.debugClearMessages = {
    testClearMessages,
    testConversationChange,
    checkEventListeners,
    simulateNewConversation,
    checkCurrentState,
    monitorEvents
};

console.log('🎯 Debug functions available:');
console.log('- window.debugClearMessages.testClearMessages()');
console.log('- window.debugClearMessages.testConversationChange()');
console.log('- window.debugClearMessages.simulateNewConversation()');
console.log('- window.debugClearMessages.checkCurrentState()');

console.log('🔧 Debug script loaded successfully!');
