# Manual Test Steps - Clear Messages

## 🎯 <PERSON>ục tiêu
Test xem messages có được clear khi click "Bắt đầu chủ đề mới" hay không.

## 📋 Test Steps

### Bước 1: <PERSON><PERSON><PERSON> bị
1. Mở `http://localhost:3001`
2. <PERSON><PERSON><PERSON> nhập vào hệ thống
3. <PERSON><PERSON><PERSON> một conversation có messages (không phải ID = 0)

### Bước 2: Mở Developer Tools
1. Nhấn F12 để mở Developer Tools
2. <PERSON><PERSON><PERSON><PERSON> sang tab **Console**
3. Paste đoạn code debug sau vào console:

```javascript
// Monitor events và messages
console.log('🔧 Starting manual test...');

// Monitor custom events
window.addEventListener('clearMessages', (e) => {
    console.log('🧹 clearMessages event received:', e.detail);
});

window.addEventListener('conversationChange', (e) => {
    console.log('🔄 conversationChange event received:', e.detail);
});

// Function to check current messages
function checkMessages() {
    const messageElements = document.querySelectorAll('[class*="message"], .message, [data-message]');
    console.log('📊 Current messages in DOM:', messageElements.length);
    return messageElements.length;
}

// Function to manually clear messages (for testing)
function manualClearMessages() {
    console.log('🧪 Manually dispatching clearMessages event...');
    window.dispatchEvent(new CustomEvent('clearMessages', {
        detail: { reason: 'manual_test', conversationId: '0' }
    }));
}

// Expose functions
window.testFunctions = { checkMessages, manualClearMessages };
console.log('✅ Test functions loaded. Use window.testFunctions.checkMessages() or window.testFunctions.manualClearMessages()');
```

### Bước 3: Test Manual Clear
1. Chạy `window.testFunctions.checkMessages()` để đếm messages hiện tại
2. Chạy `window.testFunctions.manualClearMessages()` để test clear manually
3. Chạy lại `window.testFunctions.checkMessages()` để xem messages có bị clear không

### Bước 4: Test Button Click
1. Đảm bảo bạn đang ở conversation có messages
2. Click nút **"Bắt đầu chủ đề mới"** trong sidebar
3. Quan sát console logs
4. Kiểm tra UI có clear messages không

### Bước 5: Debug nếu không hoạt động

#### Nếu không thấy console logs:
```javascript
// Check if event listeners are working
console.log('Testing event listeners...');
window.dispatchEvent(new CustomEvent('clearMessages', { detail: { test: true } }));
window.dispatchEvent(new CustomEvent('conversationChange', { detail: { test: true } }));
```

#### Nếu messages không clear:
```javascript
// Force clear messages directly
const messageContainer = document.querySelector('[class*="message-list"], .messages, main');
if (messageContainer) {
    console.log('Found message container:', messageContainer);
    // Look for React fiber to access state
    const reactFiber = messageContainer._reactInternalFiber || 
                      messageContainer._reactInternalInstance ||
                      Object.keys(messageContainer).find(key => key.startsWith('__reactInternalInstance'));
    console.log('React fiber:', reactFiber);
}
```

#### Check URL và conversation ID:
```javascript
console.log('Current URL:', window.location.pathname);
console.log('Expected URL for new conversation: /chat/0');
```

## 🔍 Expected Results

### Console Logs khi click "Bắt đầu chủ đề mới":
```
🧹 clearMessages event received: {reason: "new_conversation", conversationId: "0"}
🔄 conversationChange event received: {conversationId: "0"}
🔄 [ChatInterface] Conversation change event: [old_id] -> 0
🧹 [ChatInterface] Clearing messages for conversation change
🆕 [ChatInterface] Detected conversation ID = 0, force clearing messages
🔄 [useMessages] Effect triggered for conversation: 0
🆕 [useMessages] New conversation (ID: 0) - keeping messages empty, no API call
```

### UI Changes:
- Messages disappear immediately
- URL changes to `/chat/0`
- Input form remains active
- No loading spinner

## 🚨 Troubleshooting

### Vấn đề 1: Không thấy console logs
**Nguyên nhân**: Event listeners chưa được đăng ký
**Giải pháp**: Refresh trang và thử lại

### Vấn đề 2: Có logs nhưng messages không clear
**Nguyên nhân**: React state không được update
**Giải pháp**: Kiểm tra setMessages function

### Vấn đề 3: Messages clear chậm
**Nguyên nhân**: Race condition
**Giải pháp**: Kiểm tra thứ tự events

### Vấn đề 4: URL không đổi
**Nguyên nhân**: Router navigation issue
**Giải pháp**: Kiểm tra router.push('/chat/0')

## 📊 Success Criteria

- ✅ Console logs xuất hiện đúng thứ tự
- ✅ Messages clear ngay lập tức
- ✅ URL thay đổi thành `/chat/0`
- ✅ Không có API call với id=0
- ✅ UI responsive và smooth

## 🔧 Additional Debug Commands

```javascript
// Check React DevTools
console.log('React DevTools available:', !!window.__REACT_DEVTOOLS_GLOBAL_HOOK__);

// Check if we're in development mode
console.log('Development mode:', process.env.NODE_ENV);

// Monitor all custom events
const originalDispatch = window.dispatchEvent;
window.dispatchEvent = function(event) {
    if (event.type.includes('conversation') || event.type.includes('message')) {
        console.log(`🔔 Custom event: ${event.type}`, event.detail);
    }
    return originalDispatch.call(this, event);
};
```
