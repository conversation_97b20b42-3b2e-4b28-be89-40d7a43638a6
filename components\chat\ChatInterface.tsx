"use client"

import React, { useEffect, useState } from 'react'
import { LoadingContent } from '@/components/ui/LoadingSpinner'
import { useMessageChat } from '@/hooks/useChat'
import { useCreditMonitor } from '@/hooks/useCreditMonitor'
import { SendChat } from './sendChat'
import { MessageList } from './MessageList'
import { useWebsiteInfo } from '@/hooks/useWebsiteInfo'

export interface ChatInterfaceProps {
  conversationId: string
  promptId: string
  shopId: string
  userId: string
  onConversationUpdate?: (oldId: string, newId: string, newName?: string) => void
}

export function ChatInterface({ conversationId: propConversationId, promptId, onConversationUpdate }: ChatInterfaceProps) {
  // State for current conversation (can be updated via events)
  const [currentConversationId, setCurrentConversationId] = useState(propConversationId);
  const { websiteInfo, isLoading } = useWebsiteInfo()

  // Monitor credit and show warnings
  useCreditMonitor()

  // Use current conversation ID for messages
  const {
    messages,
    setMessages,
    isLoadingMessages,
    messagesEndRef,
    hasToken
  } = useMessageChat({ conversationId: currentConversationId })

  console.log('🖥️ [ChatInterface] Render - Messages count:', messages.length, 'Prop ID:', propConversationId, 'Current ID:', currentConversationId);

  // Listen for conversation changes from sidebar
  useEffect(() => {
    const handleConversationChange = (event: CustomEvent) => {
      const newConversationId = event.detail.conversationId;
      if (newConversationId !== currentConversationId) {
        console.log('🔄 [ChatInterface] Conversation change:', currentConversationId, '->', newConversationId);

        // If switching to new conversation (ID = '0'), clear messages immediately
        if (newConversationId === '0') {
          console.log('🧹 [ChatInterface] Clearing messages for new conversation');
          setMessages([]);
        }

        setCurrentConversationId(newConversationId);
      }
    };

    window.addEventListener('conversationChange', handleConversationChange as EventListener);
    return () => window.removeEventListener('conversationChange', handleConversationChange as EventListener);
  }, [currentConversationId, setMessages]);

  // Sync with prop changes (for initial load or direct URL access)
  useEffect(() => {
    if (propConversationId !== currentConversationId) {
      console.log('🔄 [ChatInterface] Prop conversation change:', currentConversationId, '->', propConversationId);

      // If switching to new conversation (ID = '0'), clear messages immediately
      if (propConversationId === '0') {
        console.log('🧹 [ChatInterface] Clearing messages for new conversation (prop change)');
        setMessages([]);
      }

      setCurrentConversationId(propConversationId);
    }
  }, [propConversationId, currentConversationId, setMessages]);

  // Handle browser back/forward navigation
  useEffect(() => {
    const handlePopState = () => {
      const pathParts = window.location.pathname.split('/');
      const urlConversationId = pathParts[pathParts.length - 1];

      if (urlConversationId && urlConversationId !== currentConversationId) {
        console.log('🔙 [ChatInterface] Browser navigation detected:', urlConversationId);
        setCurrentConversationId(urlConversationId);
      }
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, [currentConversationId]);

  // Tự động scroll xuống cuối khi có tin nhắn mới
  React.useEffect(() => {
    if (messagesEndRef?.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Show loading state if no token
  if (!hasToken) {
    return (
      <div className="flex items-center justify-center h-full">
        <LoadingContent text="Đang tải..." />
      </div>
    )
  }

  return (
    <>
      <div className="relative flex-1">
        {/* Messages Area */}
        <MessageList
          messages={messages}
          isLoading={isLoadingMessages}
          messagesEndRef={messagesEndRef}
        />
      </div>
      <div className='flex flex-col w-full transition-all duration-300 md:p-4 md:pb-0'>
        <SendChat
          conversationId={currentConversationId}
          promptId={promptId}
          onConversationUpdate={onConversationUpdate}
          messages={messages}
          setMessages={setMessages}
        />
        <p className='my-1 px-2 text-center text-xs text-gray-500 md:my-2 dark:text-gray-400'>{websiteInfo.meta_title} có thể mắc sai sót, vui lòng xác thực các thông tin quan trọng</p>
      </div>
    </>

  )
}

export default ChatInterface;