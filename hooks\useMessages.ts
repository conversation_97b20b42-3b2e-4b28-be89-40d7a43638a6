import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { ChatService } from '@/services/chatService';
import { useAuthData } from '@/hooks/useAuthData';
import type { Message } from '@/types';

/**
 * Hook for managing chat messages
 * Provides loading, setting, and auto-scrolling functionality
 */
export const useMessages = (conversationId: string) => {
    const [messages, setMessages] = useState<Message[]>([]);
    const [isLoadingMessages, setIsLoadingMessages] = useState(false);
    const messagesEndRef = useRef<HTMLDivElement>(null);

    const { token, shop_id, hasToken } = useAuthData();

    // Create stable dependencies to avoid useEffect array size changes
    const stableDeps = useMemo(() => ({
        conversationId: conversationId || '',
        hasToken: Boolean(hasToken),
        shopId: shop_id || ''
    }), [conversationId, hasToken, shop_id]);

    /**
     * Scroll to bottom of messages
     */
    const scrollToBottom = useCallback(() => {
        if (messagesEndRef.current) {
            messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
        }
    }, []);

    /**
     * Load messages for current conversation
     */
    const loadMessages = useCallback(async () => {

        // Don't load messages for new conversations (id = "0") or when no token
        if (!conversationId || conversationId === "0" || !hasToken) {
            setMessages([]);
            setIsLoadingMessages(false);
            return;
        }

        setIsLoadingMessages(true);
        try {
            const response = await ChatService.getMessages<Message[]>(
                conversationId,
                shop_id || undefined
            );
            if (response.data && Array.isArray(response.data)) {
                setMessages(response.data);
            } else {
                setMessages([]);
            }
        } catch (error) {
            console.error('❌ [useMessages] Error loading messages for', conversationId, ':', error);
            setMessages([]);
        } finally {
            setIsLoadingMessages(false);
        }
    }, [conversationId, hasToken, token, shop_id]);

    /**
     * Add a new message to the list
     */
    const addMessage = useCallback((message: Message) => {
        setMessages(prev => [...prev, message]);
    }, []);

    /**
     * Update a specific message
     */
    const updateMessage = useCallback((messageId: string, updates: Partial<Message>) => {
        setMessages(prev => prev.map(msg =>
            msg._id === messageId ? { ...msg, ...updates } : msg
        ));
    }, []);

    /**
     * Remove a message
     */
    const removeMessage = useCallback((messageId: string) => {
        setMessages(prev => prev.filter(msg => msg._id !== messageId));
    }, []);

    /**
     * Clear all messages
     */
    const clearMessages = useCallback(() => {
        setMessages([]);
    }, []);

    // Clear messages immediately when conversation changes, then load new ones
    useEffect(() => {
        const { conversationId: convId, hasToken: hasAuth, shopId } = stableDeps;
        console.log('🔄 [useMessages] Effect triggered for conversation:', convId);

        // Clear messages immediately to prevent showing old messages
        setMessages([]);
        setIsLoadingMessages(false);

        // Early return for new conversations (id = "0") - don't load any messages
        if (!convId || convId === "0") {
            console.log('🆕 [useMessages] New conversation detected (ID: 0), keeping messages empty');
            return;
        }

        // Early return if no auth token
        if (!hasAuth) {
            console.log('🔒 [useMessages] No auth token, keeping messages empty');
            return;
        }

        // Load messages for existing conversations
        const loadMessagesForConversation = async () => {
            console.log('📥 [useMessages] Loading messages for conversation:', convId);
            setIsLoadingMessages(true);

            try {
                const response = await ChatService.getMessages<Message[]>(
                    convId,
                    shopId || undefined
                );

                if (response.data && Array.isArray(response.data)) {
                    console.log('✅ [useMessages] Loaded', response.data.length, 'messages for conversation:', convId);
                    setMessages(response.data);
                } else {
                    console.log('📭 [useMessages] No messages found for conversation:', convId);
                    setMessages([]);
                }
            } catch (error) {
                console.error('❌ [useMessages] Error loading messages for', convId, ':', error);
                setMessages([]);
            } finally {
                setIsLoadingMessages(false);
            }
        };

        loadMessagesForConversation();
    }, [stableDeps]); // Single stable dependency

    // Listen for clear messages event (for new conversation)
    useEffect(() => {
        const handleClearMessages = (event: CustomEvent) => {
            console.log('🧹 [useMessages] Clear messages event received:', event.detail);
            setMessages([]);
        };

        window.addEventListener('clearMessages', handleClearMessages as EventListener);
        return () => window.removeEventListener('clearMessages', handleClearMessages as EventListener);
    }, []);

    // Auto-scroll when messages change
    useEffect(() => {
        scrollToBottom();
    }, [messages, scrollToBottom]);

    return {
        // State
        messages,
        isLoadingMessages,
        messagesEndRef,
        hasToken,

        // Actions
        setMessages,
        loadMessages,
        addMessage,
        updateMessage,
        removeMessage,
        clearMessages,
        scrollToBottom
    };
};

/**
 * Hook for message statistics
 */
export const useMessageStats = (messages: Message[]) => {
    return {
        total: messages.length,
        userMessages: messages.filter(msg => msg.type === 1).length,
        aiMessages: messages.filter(msg => msg.type === 0).length,
        isEmpty: messages.length === 0
    };
};
