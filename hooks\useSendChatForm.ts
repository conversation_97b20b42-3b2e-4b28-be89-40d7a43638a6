import { useState } from 'react'
import { useMessages } from '@/hooks/useMessages'
import { useAuthData, useChatPayload } from '@/hooks/useAuthData'
import { useUserCredits } from '@/hooks/useAuthStore'
import { checkCreditAndShowToast } from '@/lib/creditUtils'
import { ChatService, chatUtils } from '@/services/chatService'

import type { Message } from '@/types'

/**
 * Hook for handling chat form submission
 * Separated business logic from UI components
 */
interface UseSendChatFormProps {
  conversationId: string
  promptId: string
  onConversationUpdate?: (oldId: string, newId: string, newName?: string) => void
  messages?: Message[]
  setMessages?: React.Dispatch<React.SetStateAction<Message[]>>
}

export const useSendChatForm = ({ conversationId, promptId, onConversationUpdate, messages: externalMessages, setMessages: externalSetMessages }: UseSendChatFormProps) => {
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  // Get auth data and chat utilities
  const { hasToken } = useAuthData()
  const { createPayload } = useChatPayload()

  // Get user credits for validation
  const { credit, credit_use } = useUserCredits()

  // Use external messages state if provided, otherwise use internal
  const internalMessagesHook = useMessages(externalMessages ? '' : conversationId) // Only use internal if no external
  const messages = externalMessages || internalMessagesHook.messages
  const setMessages = externalSetMessages || internalMessagesHook.setMessages

  console.log('🔧 [useSendChatForm] Using messages from:', externalMessages ? 'EXTERNAL' : 'INTERNAL', 'for conversation:', conversationId);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!inputValue.trim() || isLoading || !hasToken) return

    // Kiểm tra credit trước khi gửi tin nhắn
    if (!checkCreditAndShowToast(credit, credit_use)) {
      return
    }

    console.log('🚀 Starting chat submission:', {
      conversationId,
      query: inputValue,
      promptId,
      hasToken,
      credit: `${credit_use}/${credit}`
    });

    // Create user message
    const userMessage = ChatService.createTempUserMessage(inputValue, conversationId)
    console.log('👤 Created user message:', userMessage);

    setMessages((prev: Message[]) => {
      const newMessages = [...prev, userMessage];
      return newMessages;
    })
    setIsLoading(true)

    try {
      // Create chat payload
      const payload = createPayload(conversationId, inputValue, promptId)

      // Send message via chat service
      const stream = await ChatService.sendMessage(payload)

      if (stream) {
        const reader = stream.getReader()
        let aiContent = ''

        // Create AI message placeholder
        const aiMessage = ChatService.createTempAIMessage(conversationId)

        setMessages((prev: Message[]) => {
          const newMessages = [...prev, aiMessage];
          return newMessages;
        })

        // Track if we've found a new conversation ID during streaming
        let newConversationId: string | null = null;
        let hasProcessedConversationUpdate = false;

        // Process streaming response
        await chatUtils.processStreamingResponse(
          reader,
          (content) => {
            aiContent += content
            setMessages((prev: Message[]) => {
              const updatedMessages = prev.map((msg: Message) =>
                msg._id === aiMessage._id ? { ...msg, content: aiContent } : msg
              );
              return updatedMessages;
            })
          },
          (finalData) => {
            console.log('🏁 Stream ended, final data:', finalData);

            // Handle conversation creation for new conversations (ID = '0')
            if (conversationId === '0' && !hasProcessedConversationUpdate && onConversationUpdate) {
              // Try to extract conversation ID from various sources
              const extractedId = newConversationId ||
                (finalData?.conversation_id) ||
                (finalData?._id) ||
                (finalData?.id);

              if (extractedId && extractedId !== '0') {
                console.log('✅ New conversation created with ID:', extractedId);

                // Store the extracted ID
                newConversationId = extractedId;

                // Generate conversation title from user input (first 50 chars)
                const newTitle = inputValue.length > 50
                  ? inputValue.substring(0, 50) + '...'
                  : inputValue;

                console.log('📝 New conversation title:', newTitle);

                // Update conversation ID and title
                onConversationUpdate('0', extractedId, newTitle);
                hasProcessedConversationUpdate = true;
              } else {
                console.log('⚠️ No new conversation ID found in response');
              }
            }
          },
          // Callback for immediate conversation ID detection
          (conversationId) => {
            if (conversationId && conversationId !== '0' && !newConversationId) {
              console.log('🚀 Conversation ID detected immediately:', conversationId);
              newConversationId = conversationId;

              // Process conversation update immediately if we haven't done it yet
              if (!hasProcessedConversationUpdate && onConversationUpdate) {
                const newTitle = inputValue.length > 50
                  ? inputValue.substring(0, 50) + '...'
                  : inputValue;

                console.log('📝 Immediate conversation title:', newTitle);
                onConversationUpdate('0', conversationId, newTitle);
                hasProcessedConversationUpdate = true;
              }
            }
          }
        )

        // Additional check: if we still haven't processed conversation update,
        // try to extract from AI message response
        if (conversationId === '0' && !hasProcessedConversationUpdate && onConversationUpdate) {
          // Look for conversation ID in the AI message
          const aiMessageConversationId = aiMessage.conversation_id;
          if (aiMessageConversationId && aiMessageConversationId !== '0') {
            console.log('✅ Found conversation ID in AI message:', aiMessageConversationId);

            const newTitle = inputValue.length > 50
              ? inputValue.substring(0, 50) + '...'
              : inputValue;

            onConversationUpdate('0', aiMessageConversationId, newTitle);
            hasProcessedConversationUpdate = true;
          }
        }

        // Update message conversation IDs if we found a new conversation ID
        if (conversationId === '0' && hasProcessedConversationUpdate) {
          console.log('🔄 Conversation update processed successfully');
        }
      } else {
        console.log('❌ No stream received from API');
      }
    } catch (error) {
      console.error('Error sending message:', error)
      // Remove user message on error
      setMessages((prev: Message[]) => prev.filter((msg: Message) => msg._id !== userMessage._id))
    } finally {
      setIsLoading(false)
      setInputValue('')
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e as any)
    }
  }

  // Kiểm tra xem có hết credit không
  const isOutOfCredit = credit > 0 && credit_use >= credit

  return {
    inputValue,
    setInputValue,
    isLoading,
    handleSubmit,
    handleKeyPress,
    // Credit info
    credit,
    credit_use,
    isOutOfCredit
  }
}