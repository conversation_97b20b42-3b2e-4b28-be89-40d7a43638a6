# Test: C<PERSON><PERSON> nhật Conversation từ chat/0

## 🎯 Mục tiêu
Kiểm tra luồng: chat/0 → gử<PERSON> tin nhắn → tự động cập nhật thành conversation ID mới + đổi tên theo nội dung chat

## 📋 Test Steps

### 1. Chuẩn bị
- Mở `http://localhost:3001`
- Đăng nhập thành công
- Click "Tạo chủ đề mới" để vào `/chat/0`
- Đảm bảo chat interface trống

### 2. Kiểm tra trạng thái ban đầu
- URL: `/chat/0`
- <PERSON>bar hiển thị "Cuộc trò chuyện mới"
- Chat interface trống
- Input form hoạt động

### 3. Test gửi tin nhắn đầu tiên
1. Mở Developer Tools (F12) → Console tab
2. Gõ tin nhắn vào input (ví dụ: "Chào bạn! Tôi có thể hỏi về AI không?")
3. <PERSON><PERSON><PERSON><PERSON> Enter hoặc click Send
4. Quan sát:
   - Console logs
   - Network requests
   - UI changes

## ✅ Kết quả mong đợi

### Console Logs:
```
🚀 Starting chat submission: {conversationId: "0", query: "Chào bạn! Tôi có thể hỏi về AI không?", ...}
👤 Created user message: {_id: "temp-...", content: "Chào bạn! Tôi có thể hỏi về AI không?", ...}
🔄 Starting stream processing...
📦 Raw chunk: data: {"event":"start","conversation_id":"67a1b2c3d4e5f6789012345"}
🆕 Found conversation ID in start event: 67a1b2c3d4e5f6789012345
📦 Raw chunk: data: {"event":"message","content":"Chào bạn! "}
💬 Adding content: Chào bạn! 
📦 Raw chunk: data: {"event":"message","content":"Tôi có thể giúp bạn..."}
💬 Adding content: Tôi có thể giúp bạn...
📦 Raw chunk: data: {"event":"end"}
🏁 Stream ended, final data: {conversation_id: "67a1b2c3d4e5f6789012345", ...}
✅ New conversation created with ID: 67a1b2c3d4e5f6789012345
📝 New conversation title: Chào bạn! Tôi có thể hỏi về AI không?
```

### Network Requests:
- ✅ POST `/api/chat` với payload conversation_id: "0"
- ✅ Response stream với conversation_id mới
- ❌ **KHÔNG** có GET request để load messages

### UI Changes:
- ✅ URL thay đổi từ `/chat/0` thành `/chat/67a1b2c3d4e5f6789012345`
- ✅ Sidebar title thay đổi từ "Cuộc trò chuyện mới" thành "Chào bạn! Tôi có thể hỏi về AI không?"
- ✅ Messages hiển thị: user message + AI response
- ✅ Input form reset và sẵn sàng cho tin nhắn tiếp theo

## 🔍 Debug Points

### Nếu conversation ID không được extract:
```javascript
// Paste vào console để monitor streaming response
window.addEventListener('beforeunload', () => {
    console.log('Page unloading...');
});

// Monitor fetch requests
const originalFetch = window.fetch;
window.fetch = function(...args) {
    console.log('🌐 Fetch request:', args);
    return originalFetch.apply(this, args).then(response => {
        console.log('📡 Fetch response:', response);
        return response;
    });
};
```

### Nếu URL không cập nhật:
```javascript
// Monitor history changes
const originalPushState = history.pushState;
const originalReplaceState = history.replaceState;

history.pushState = function(...args) {
    console.log('📍 pushState:', args);
    return originalPushState.apply(this, args);
};

history.replaceState = function(...args) {
    console.log('📍 replaceState:', args);
    return originalReplaceState.apply(this, args);
};
```

### Nếu sidebar không cập nhật:
```javascript
// Monitor custom events
window.addEventListener('conversationUpdated', (e) => {
    console.log('🔄 conversationUpdated event:', e.detail);
});

window.addEventListener('conversationUpdate', (e) => {
    console.log('🔄 conversationUpdate event:', e.detail);
});
```

## 🧪 Test Cases

### Case 1: Tin nhắn ngắn
- **Input**: "Hello"
- **Expected Title**: "Hello"
- **Expected**: URL update + sidebar update

### Case 2: Tin nhắn dài
- **Input**: "Tôi muốn hỏi về cách sử dụng AI trong việc phát triển ứng dụng web hiện đại"
- **Expected Title**: "Tôi muốn hỏi về cách sử dụng AI trong việc phát..."
- **Expected**: Title bị cắt ở 50 ký tự

### Case 3: Tin nhắn có ký tự đặc biệt
- **Input**: "Xin chào! 👋 Bạn có thể giúp tôi không?"
- **Expected Title**: "Xin chào! 👋 Bạn có thể giúp tôi không?"
- **Expected**: Ký tự emoji được preserve

## 📊 Performance Check

### Thời gian phản hồi:
- Message send: **< 500ms**
- URL update: **< 200ms**
- Sidebar update: **< 300ms**
- Stream processing: **< 2s**

### Memory usage:
- Không có memory leak
- Event listeners được cleanup đúng cách

## 🔧 Troubleshooting

### Vấn đề thường gặp:

1. **Conversation ID không được extract**
   - Kiểm tra API response format
   - Kiểm tra `parseStreamingResponse` logic
   - Kiểm tra event types từ server

2. **URL không cập nhật**
   - Kiểm tra `onConversationUpdate` callback
   - Kiểm tra `handleConversationUpdate` trong MainContent
   - Kiểm tra router navigation

3. **Sidebar không cập nhật**
   - Kiểm tra conversation list state update
   - Kiểm tra `conversationUpdated` event dispatch
   - Kiểm tra sidebar re-render

4. **Title không đúng**
   - Kiểm tra input value capture
   - Kiểm tra title generation logic
   - Kiểm tra character limit (50 chars)

## ✨ Success Criteria

- ✅ Conversation ID được extract từ API response
- ✅ URL cập nhật từ `/chat/0` thành `/chat/[new_id]`
- ✅ Sidebar title cập nhật theo nội dung tin nhắn
- ✅ Messages hiển thị đúng
- ✅ Input form reset sau khi gửi
- ✅ Không có console errors
- ✅ Performance tốt (< 2s total)

## 🎯 Next Steps

Sau khi test thành công, có thể test thêm:
- Gửi tin nhắn thứ 2 trong conversation mới
- Chuyển sang conversation khác rồi quay lại
- Refresh page và kiểm tra persistence
