import { EP } from "@/configs/constants/api";
import { apiCall, handleApiError } from "@/lib/apiUtils";
import { DataResponseType, ChatPayload, Message } from "@/types";
import { getCookie } from 'cookies-next/client';
import { COOKIE_KEYS } from '@/types/auth';

/**
 * Chat service for handling chat-related API calls
 */
export class ChatService {
    private static readonly CHAT_API_URL = 'https://fchatai-api.salekit.com:3034/api/v1/assistant/chat';

    /**
     * Send a chat message with streaming response (POST chat)
     * Uses token from cookie as per requirement
     */
    static async sendMessage(payload: ChatPayload): Promise<ReadableStream<Uint8Array> | null> {
        try {
            const token = getCookie(COOKIE_KEYS.TOKEN) as string;
            if (!token) {
                console.error('❌ No authentication token found');
                throw new Error('No authentication token found');
            }

            console.log('🌐 Sending POST request to:', this.CHAT_API_URL);
            console.log('📦 Payload:', payload);
            console.log('🔑 Token:', token.substring(0, 20) + '...');

            const response = await fetch(this.CHAT_API_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'token': token
                },
                body: JSON.stringify(payload)
            });

            console.log('📡 API Response status:', response.status);
            console.log('📡 API Response headers:', Object.fromEntries(response.headers.entries()));

            if (!response.ok) {
                const errorText = await response.text();
                console.error('❌ API Error Response:', errorText);
                throw new Error(`Chat API Error: ${response.status} ${response.statusText}`);
            }

            console.log('✅ API response OK, returning stream');
            return response.body;
        } catch (error) {
            console.error('❌ Error sending chat message:', error);
            return null;
        }
    }

    /**
     * Get messages for a conversation (GET message)
     * Uses token from cookie as per requirement
     */
    static async getMessages<T>(
        conversationId: string,
        shop_id?: string
    ): Promise<DataResponseType<T>> {
        try {
            const token = getCookie(COOKIE_KEYS.TOKEN) as string;
            if (!token) {
                throw new Error('No authentication token found');
            }

            return await apiCall<T>(
                'GET',
                [EP.API, EP.V1, EP.MESSAGE, EP.CONVERSATION],
                undefined,
                { conversation_id: conversationId },
                token,
                shop_id
            );
        } catch (error) {
            return handleApiError(error, 'Get Messages');
        }
    }

    /**
     * Create a temporary user message
     */
    static createTempUserMessage(content: string, conversationId: string): Message {
        const currentTime = new Date().toISOString();
        return {
            _id: `temp-${Date.now()}`,
            type: 1, // User message
            content,
            albums: [],
            prompt_tokens: 0,
            completion_tokens: 0,
            total_tokens: 0,
            created_at: currentTime,
            updated_at: currentTime,
            conversation_id: conversationId
        };
    }

    /**
     * Create a temporary AI message
     */
    static createTempAIMessage(conversationId: string): Message {
        const currentTime = new Date().toISOString();
        return {
            _id: `ai-${Date.now()}`,
            type: 0, // AI message
            content: '',
            albums: [],
            prompt_tokens: 0,
            completion_tokens: 0,
            total_tokens: 0,
            created_at: currentTime,
            updated_at: currentTime,
            conversation_id: conversationId
        };
    }

    /**
     * Parse streaming response
     * Handles both "data: {...}" and direct JSON formats
     */
    static parseStreamingResponse(chunk: string): any | null {
        try {
            let jsonStr = chunk.trim();

            // Handle Server-Sent Events format: "data: {...}"
            if (jsonStr.startsWith('data: ')) {
                jsonStr = jsonStr.substring(6); // Remove "data: " prefix
            }

            // Skip empty lines or non-JSON content
            if (!jsonStr || jsonStr === '') {
                return null;
            }

            const data = JSON.parse(jsonStr);

            // Return the full data object to preserve all fields
            return {
                event: data.event,
                content: data.content,
                conversation_id: data.conversation_id,
                _id: data._id,
                id: data.id,
                ...data // Include any other fields
            };
        } catch (error) {
            console.log('Failed to parse chunk:', chunk, error);
            return null;
        }
    }
}

/**
 * Utility functions for chat operations
 */
export const chatUtils = {
    /**
     * Process streaming chat response
     */
    async processStreamingResponse(
        reader: ReadableStreamDefaultReader<Uint8Array>,
        onContent: (content: string) => void,
        onEnd: (finalData?: any) => void,
        onConversationId?: (conversationId: string) => void
    ): Promise<void> {
        const decoder = new TextDecoder();
        let done = false;
        let finalData: any = null;

        console.log('🔄 Starting stream processing...');

        while (!done) {
            const { value, done: streamDone } = await reader.read();
            done = streamDone;

            if (value) {
                const chunk = decoder.decode(value, { stream: true });
                console.log('📦 Raw chunk:', chunk);

                // Split by lines and process each line
                const lines = chunk.split('\n');

                for (const line of lines) {
                    const trimmedLine = line.trim();
                    if (trimmedLine) {
                        console.log('📝 Processing line:', trimmedLine);

                        const parsed = ChatService.parseStreamingResponse(trimmedLine);
                        console.log('🔍 Parsed result:', parsed);

                        if (parsed) {
                            if (parsed.event === 'message' && parsed.content) {
                                console.log('💬 Adding content:', parsed.content);
                                onContent(parsed.content);

                                // Check if this message contains conversation ID
                                if (parsed.conversation_id && !finalData?.conversation_id) {
                                    console.log('🆕 Found conversation ID in message:', parsed.conversation_id);
                                    finalData = { ...finalData, ...parsed };

                                    // Immediately notify about conversation ID
                                    if (onConversationId) {
                                        onConversationId(parsed.conversation_id);
                                    }
                                }
                            } else if (parsed.event === 'end') {
                                console.log('🏁 Stream ended');
                                onEnd(finalData);
                                return; // Exit early when stream ends
                            } else if (parsed.event === 'start') {
                                console.log('🚀 Stream started');

                                // Check if start event contains conversation ID
                                if (parsed.conversation_id) {
                                    console.log('🆕 Found conversation ID in start event:', parsed.conversation_id);
                                    finalData = { ...finalData, ...parsed };

                                    // Immediately notify about conversation ID
                                    if (onConversationId) {
                                        onConversationId(parsed.conversation_id);
                                    }
                                }
                            } else if (parsed.event === 'conversation_created' || parsed.event === 'conversation') {
                                console.log('🆕 Conversation data received:', parsed);
                                finalData = { ...finalData, ...parsed };
                            } else {
                                // Check any other event for conversation ID
                                if (parsed.conversation_id && !finalData?.conversation_id) {
                                    console.log('🆕 Found conversation ID in event:', parsed.event, parsed.conversation_id);
                                    finalData = { ...finalData, ...parsed };
                                }
                            }
                        }
                    }
                }
            }
        }

        console.log('✅ Stream processing completed');
        onEnd(finalData);
    }
};
