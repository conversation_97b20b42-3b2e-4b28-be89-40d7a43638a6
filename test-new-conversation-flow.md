# Test: <PERSON><PERSON><PERSON> "Tạo chủ đề mới"

## 🎯 Mục tiêu
Đ<PERSON> bảo khi click "Tạo chủ đề mới", chat interface sẽ hiển thị trạng thái trắng (không có messages) và KHÔNG gọi API với id=0.

## 📋 Test Steps

### 1. <PERSON><PERSON>n bị
- Mở ứng dụng tại `http://localhost:3001`
- Đ<PERSON>ng nhập thành công
- Chọn một conversation có messages (ID khác 0)

### 2. Kiểm tra trạng thái ban đầu
- URL: `/chat/[conversation_id]` (ví dụ: `/chat/687e5e6b022870ca73434cba`)
- Chat interface hiển thị messages của conversation hiện tại
- Sidebar hiển thị danh sách conversations

### 3. Test chức năng "Tạo chủ đề mới"
1. Mở Developer Tools (F12) → Network tab
2. Click nút **"Tạo chủ đề mới"** trong sidebar
3. <PERSON>uan sát:
   - Console logs
   - Network requests
   - U<PERSON> changes

## ✅ Kết quả mong đợi

### Console Logs:
```
🔄 [ChatInterface] Conversation change event: [old_id] -> 0
🧹 [ChatInterface] Clearing messages for conversation change
🔄 [useMessages] Effect triggered for conversation: 0
🆕 [useMessages] New conversation (ID: 0) - keeping messages empty, no API call
```

### Network Requests:
- ❌ **KHÔNG** có request đến `/api/v1/message/conversation?conversation_id=0`
- ❌ **KHÔNG** có request đến `/api/v1/message/conversation?conversation_id=[old_id]`
- ✅ Chỉ có các request cần thiết khác (nếu có)

### UI Changes:
- ✅ URL thay đổi thành `/chat/0`
- ✅ Messages được clear ngay lập tức
- ✅ Chat interface hiển thị trạng thái trắng
- ✅ Không có loading spinner cho messages
- ✅ Input form vẫn hoạt động bình thường
- ✅ Toast notification "Đã bắt đầu cuộc trò chuyện mới"

## 🔍 Debug Points

### Nếu vẫn có API call với id=0:
1. Kiểm tra `useMessages.ts` line 104-108
2. Đảm bảo early return cho `conversationId === "0"`

### Nếu messages không clear ngay lập tức:
1. Kiểm tra `ChatInterface.tsx` event listener
2. Kiểm tra `useConversation.ts` dispatch event

### Nếu URL không cập nhật:
1. Kiểm tra `window.history.replaceState` trong `handleCreateNewConversation`

## 🧪 Test Cases

### Case 1: Từ conversation có messages
- **Input**: Đang ở conversation có 5 messages
- **Action**: Click "Tạo chủ đề mới"
- **Expected**: Messages clear → URL `/chat/0` → Trạng thái trắng

### Case 2: Từ conversation rỗng
- **Input**: Đang ở conversation không có messages
- **Action**: Click "Tạo chủ đề mới"
- **Expected**: Vẫn trạng thái trắng → URL `/chat/0`

### Case 3: Từ conversation id=0
- **Input**: Đang ở `/chat/0`
- **Action**: Click "Tạo chủ đề mới"
- **Expected**: Refresh trạng thái → Vẫn `/chat/0`

## 📊 Performance Check

### Thời gian phản hồi:
- Messages clear: **< 100ms**
- URL update: **< 100ms**
- UI re-render: **< 200ms**

### Memory usage:
- Không có memory leak từ event listeners
- Messages array được clear đúng cách

## 🔧 Troubleshooting

### Vấn đề thường gặp:

1. **API vẫn được gọi với id=0**
   - Kiểm tra `useMessages.ts` early return logic
   - Đảm bảo `conversationId === "0"` check đúng

2. **Messages flash (hiện tạm thời rồi mất)**
   - Kiểm tra thứ tự clear messages trong ChatInterface
   - Đảm bảo clear trước khi update conversationId

3. **Event không được dispatch**
   - Kiểm tra `handleCreateNewConversation` có dispatch event không
   - Kiểm tra event listener trong ChatInterface

4. **URL không sync**
   - Kiểm tra `window.history.replaceState`
   - Kiểm tra browser navigation handling

## ✨ Success Criteria

- ✅ Không có API call với id=0
- ✅ Messages clear ngay lập tức
- ✅ URL cập nhật thành `/chat/0`
- ✅ UI responsive và mượt mà
- ✅ Không có console errors
- ✅ Toast notification hiển thị
- ✅ Sidebar cập nhật conversation list
